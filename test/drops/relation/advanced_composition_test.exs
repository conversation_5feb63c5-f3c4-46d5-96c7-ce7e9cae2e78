defmodule Drops.Relations.AdvancedCompositionTest do
  use Drops.RelationCase, async: false

  describe "cross-relation composition" do
    @tag relations: [:users, :posts]
    test "different relation types can be composed", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Admin User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Published Post",
          body: "Content",
          published: 1,
          user_id: user1.id
        })

      # Test composition between different relation types
      admin_users = users.restrict(name: "Admin User")
      result = posts.restrict(admin_users, published: 1)

      # Verify we get some kind of relation back
      assert result != nil
      assert is_struct(result)

      # Test that it works with basic operations
      case result do
        %Drops.Relation.Composite{} ->
          # If associations are detected, we get a Composite
          assert result.left == admin_users
          assert result.repo != nil

        _ ->
          # Otherwise, we get a regular relation
          assert result.__struct__ == posts
      end
    end

    @tag relations: [:users, :posts]
    test "composition with index-based finders", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Test User", email: "<EMAIL>"})

      {:ok, _post} =
        posts.insert(%{
          title: "Test Post",
          body: "Content",
          published: 1,
          user_id: user.id
        })

      # Test composition with get_by_* functions
      user_by_email = users.get_by_email("<EMAIL>")
      result = posts.restrict(user_by_email, published: 1)

      # Verify we get some kind of relation back
      assert result != nil
      assert is_struct(result)
    end

    @tag relations: [:users, :posts]
    test "composition with non-relation modules falls back gracefully", %{
      users: _users,
      posts: posts
    } do
      # Test with a regular queryable (should not create Composite)
      regular_query = from(u in "users", where: u.name == "Test")
      restricted = posts.restrict(regular_query, published: 1)

      # Should create a regular relation, not a Composite
      refute match?(%Drops.Relation.Composite{}, restricted)
      assert restricted.__struct__ == posts
    end
  end
end
