defmodule Drops.Relations.AdvancedCompositionTest do
  use Drops.RelationCase, async: false

  # Define relations with associations
  relation(:users) do
    associations do
      has_many(:posts, Test.Relations.PostsSqlite.Struct, foreign_key: :user_id)
    end
  end

  relation(:posts) do
    associations do
      belongs_to(:user, Test.Relations.UsersSqlite.Struct, foreign_key: :user_id)
    end
  end

  describe "cross-relation composition" do
    @tag relations: [:users, :posts]
    test "different relation types can be composed", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Admin User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Published Post",
          body: "Content",
          published: 1,
          user_id: user1.id
        })

      # Test composition between different relation types
      admin_users = users.restrict(name: "Admin User")
      result = posts.restrict(admin_users, published: 1)

      # Verify we get some kind of relation back
      assert result != nil
      assert is_struct(result)

      # Test that it works with basic operations
      case result do
        %Drops.Relation.Composite{} ->
          # If associations are detected, we get a Composite
          assert result.left == admin_users
          assert result.repo != nil

        _ ->
          # Otherwise, we get a regular relation
          assert result.__struct__ == posts
      end
    end

    @tag relations: [:users, :posts]
    test "composition with index-based finders", %{users: users, posts: posts} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Test User", email: "<EMAIL>"})

      {:ok, _post} =
        posts.insert(%{
          title: "Test Post",
          body: "Content",
          published: 1,
          user_id: user.id
        })

      # Test composition with get_by_* functions
      user_by_email = users.get_by_email("<EMAIL>")
      result = posts.restrict(user_by_email, published: 1)

      # Verify we get some kind of relation back
      assert result != nil
      assert is_struct(result)
    end

    @tag relations: [:users, :posts]
    test "composition with non-relation modules falls back gracefully", %{
      users: _users,
      posts: posts
    } do
      # Test with a regular queryable (should not create Composite)
      regular_query = from(u in "users", where: u.name == "Test")
      restricted = posts.restrict(regular_query, published: 1)

      # Should create a regular relation, not a Composite
      refute match?(%Drops.Relation.Composite{}, restricted)
      assert restricted.__struct__ == posts
    end

    @tag relations: [:users, :posts]
    test "composite relations automatically preload associations", %{
      users: users,
      posts: posts
    } do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Admin User", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "Regular User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Admin Post 1",
          body: "Content 1",
          published: 1,
          user_id: user1.id
        })

      {:ok, _post2} =
        posts.insert(%{
          title: "Admin Post 2",
          body: "Content 2",
          published: 1,
          user_id: user1.id
        })

      {:ok, _post3} =
        posts.insert(%{
          title: "User Post",
          body: "Content 3",
          published: 1,
          user_id: user2.id
        })

      # Create a composite relation: admin users with published posts
      admin_users = users.restrict(name: "Admin User")

      # Create composite explicitly since we have associations defined
      composite = %Drops.Relation.Composite{
        left: admin_users,
        right: posts.restrict(published: 1),
        association: :posts,
        repo: posts.repo()
      }

      # Verify it's a Composite relation
      assert match?(%Drops.Relation.Composite{}, composite)
      assert composite.association == :posts

      # Enumerate the results - this should trigger auto-preloading
      results = Enum.to_list(composite)

      # Should get the admin user with posts preloaded
      assert length(results) == 1
      [admin_user] = results

      assert admin_user.name == "Admin User"
      assert admin_user.email == "<EMAIL>"

      # Verify posts are preloaded (not lazy-loaded)
      assert Ecto.assoc_loaded?(admin_user.posts)
      assert length(admin_user.posts) == 2

      # Verify the posts are the correct ones
      post_titles = Enum.map(admin_user.posts, & &1.title) |> Enum.sort()
      assert post_titles == ["Admin Post 1", "Admin Post 2"]

      # Verify all posts belong to the admin user
      Enum.each(admin_user.posts, fn post ->
        assert post.user_id == admin_user.id
        assert post.published == 1
      end)
    end

    @tag relations: [:users, :posts]
    test "composite relations work with index-based finders and preloading", %{
      users: users,
      posts: posts
    } do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Test User", email: "<EMAIL>"})

      {:ok, _post1} =
        posts.insert(%{
          title: "Published Post",
          body: "Content 1",
          published: 1,
          user_id: user.id
        })

      {:ok, _post2} =
        posts.insert(%{
          title: "Draft Post",
          body: "Content 2",
          published: 0,
          user_id: user.id
        })

      # Use index-based finder with composition
      user_by_email = users.get_by_email("<EMAIL>")

      # Create composite explicitly
      composite = %Drops.Relation.Composite{
        left: user_by_email,
        right: posts.restrict(published: 1),
        association: :posts,
        repo: posts.repo()
      }

      # Verify it's a Composite relation
      assert match?(%Drops.Relation.Composite{}, composite)

      # Enumerate the results
      results = Enum.to_list(composite)

      # Should get the user with only published posts preloaded
      assert length(results) == 1
      [found_user] = results

      assert found_user.email == "<EMAIL>"
      assert Ecto.assoc_loaded?(found_user.posts)
      assert length(found_user.posts) == 1
      assert hd(found_user.posts).title == "Published Post"
      assert hd(found_user.posts).published == 1
    end
  end
end
